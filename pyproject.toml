[project]
name = "snakey-brainfuck-interpreter"
version = "0.1.0"
description = "🐍 A Brainfuck interpreter with a modern CLI interface"
authors = [
    {name = "<PERSON><PERSON><PERSON><PERSON><PERSON>", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.8"
dependencies = [
    "typer>=0.9.0",
    "rich>=13.0.0"
]

[project.scripts]
snakey-bf = "snakey_brainfuck_interpreter.cli:main"

[build-system]
requires = ["setuptools>=42.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.ruff]
line-length = 88
select = ["ALL"]
ignore = [
    # Allow boolean positional arguments in Typer CLI
    "FBT002",
    # Allow broad exception catching in CLI
    "BLE001",
    # Allow unused exception variables
    "B017",
    # Allow non-abstract class with abstract methods (for Typer)
    "B024",
    # Allow non-relative imports
    "TID252",
    # Allow non-abstract class with abstract methods (for Typer)
    "B027"
]

[tool.ruff.per-file-ignores]
"src/snakey_brainfuck_interpreter/cli.py" = [
    # Allow broad exception handling in CLI
    "TRY003",
    # Allow non-abstract class with abstract methods (for Typer)
    "B024",
    # Allow unused exception variables
    "B017",
    # Allow boolean positional arguments in Typer CLI
    "FBT002",
    # Allow unused imports in __main__
    "F401"
]